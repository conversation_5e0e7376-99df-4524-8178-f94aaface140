/* 移动端默认样式 (rpx单位) */
.action-button {
	background-color: #f4f5f7;
	color: #222;
	margin: 0 5rpx;
	padding: 0;
}
/* PC端样式适配 (px单位) - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于1024px时应用PC端样式 */
/* PC端页面容器 */
.search-page.pc-mode {
	background-color: #f5f7fa;
	min-height: 100vh;
}
.search-page.pc-mode .search-container {
	max-width: 800px;
	margin: 0 auto;
	padding: 15px;
	background-color: #ffffff;
	box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	margin-top: 15px;
}
/* PC端表单组样式 */
.search-page.pc-mode .pc-form-group {
	border-radius: 8px !important;
	margin: 15px !important;
	padding: 12px;
	border: 1px solid #e1e5e9;
}
.search-page.pc-mode .pc-form-group textarea {
	font-size: 14px;
	line-height: 1.5;
	min-height: 80px;
}
/* PC端按钮组样式 */
.search-page.pc-mode .pc-btn-group {
	padding: 8px 0;
}
.search-page.pc-mode .action-button {
	background-color: #f8f9fa !important;
	color: #495057 !important;
	margin: 0 8px !important;
	padding: 8px 12px !important;
	font-size: 13px !important;
	border-radius: 4px;
	border: 1px solid #dee2e6;
	transition: all 0.2s ease;
}
.search-page.pc-mode .action-button:hover {
	background-color: #e9ecef !important;
	border-color: #adb5bd;
}
/* PC端搜索按钮样式 */
.search-page.pc-mode .pc-search-button {
	max-width: 300px;
	margin: 20px auto;
}
.search-page.pc-mode .pc-search-btn {
	font-size: 16px !important;
	padding: 12px 24px !important;
	border-radius: 6px;
	min-height: 44px;
}
/* PC端搜索动态栏样式 */
.search-page.pc-mode .search-dynamics-bar {
	border-radius: 6px;
	margin: 15px 0;
}
.search-page.pc-mode .search-dynamics-bar .action {
	font-size: 14px;
	padding: 12px 16px;
}
/* PC端搜索列表样式 */
.search-page.pc-mode .pc-search-list {
	border-radius: 6px;
	overflow: hidden;
}
.search-page.pc-mode .pc-search-item {
	padding: 16px 20px;
	border-bottom: 1px solid #f1f3f4;
	transition: background-color 0.2s ease;
}
.search-page.pc-mode .pc-search-item:hover {
	background-color: #f8f9fa;
}
.search-page.pc-mode .pc-search-item .content {
	font-size: 14px;
}
.search-page.pc-mode .pc-search-item .action {
	font-size: 12px;
}
/* 响应式断点样式 */
@media (min-width: 1024px) {
	/* 搜索页面整体布局 */
.search-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 15px;
		background-color: #ffffff;
		box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
		border-radius: 8px;
		margin-top: 15px;
}

	/* 表单输入框 */
.cu-form-group {
		border-radius: 8px !important;
		margin: 15px !important;
		padding: 12px;
		border: 1px solid #e1e5e9;
}
.cu-form-group textarea {
		font-size: 14px !important;
		line-height: 1.5;
		min-height: 80px;
}

	/* 按钮样式 */
.action-button {
		background-color: #f8f9fa !important;
		color: #495057 !important;
		margin: 0 8px !important;
		padding: 8px 12px !important;
		font-size: 13px !important;
		border-radius: 4px;
		border: 1px solid #dee2e6;
		transition: all 0.2s ease;
}
.action-button:hover {
		background-color: #e9ecef !important;
		border-color: #adb5bd;
}

	/* 搜索按钮 */
.cu-btn.lg {
		font-size: 16px !important;
		padding: 12px 24px !important;
		border-radius: 6px;
		min-height: 44px;
}

	/* 搜索列表项 */
.cu-list.menu .cu-item {
		padding: 16px 20px;
		border-bottom: 1px solid #f1f3f4;
		transition: background-color 0.2s ease;
}
.cu-list.menu .cu-item:hover {
		background-color: #f8f9fa;
}
.cu-list.menu .cu-item .content {
		font-size: 14px;
}
.cu-list.menu .cu-item .action {
		font-size: 12px;
}

	/* 搜索动态栏 */
.cu-bar {
		border-radius: 6px;
		margin: 15px 0;
}
.cu-bar .action {
		font-size: 14px;
		padding: 12px 16px;
}
}
