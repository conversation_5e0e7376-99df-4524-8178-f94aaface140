/* 移动端默认样式 (rpx单位) */
page {
	background: #f2f2f2;
}

.UCenter-bg {
	height: 550rpx;
	display: flex;
	justify-content: center;
	padding-top: 40rpx;
	overflow: hidden;
	position: relative;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-weight: 300;
	text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.UCenter-bg image {
	width: 200rpx;
	height: 200rpx;
}

.UCenter-bg .gif-wave {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	z-index: 99;
	mix-blend-mode: screen;
	height: 100rpx;
}

.menu-image {
	position: relative;
	display: block;
	margin-top: 12rpx;
	width: 52rpx;
	height: 52rpx;
}

button::after {
	border: none;
}

.cu-list.grid {
	padding: 20rpx 10rpx;
}

.cu-list.grid>.cu-item {
	padding: 20rpx 0;
}

.cu-list.grid>.cu-item text {
	font-size: 28rpx;
	margin-top: 10rpx;
}

.text-xxl {
	font-size: 46rpx !important;
}

.margin-top-sm {
	font-size: 26rpx;
}

/* PC端样式适配 (px单位) - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于1024px时应用PC端样式 */

/* PC端页面容器 */
.user-page.pc-mode {
	background-color: #f5f7fa;
	min-height: 100vh;
}

.user-page.pc-mode .user-content {
	max-width: 900px;
	margin: 0 auto;
	padding: 15px;
	background-color: #ffffff;
	box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	margin-top: 15px;
}

/* PC端用户头部样式 */
.user-page.pc-mode .pc-user-header {
	height: 280px !important;
	border-radius: 8px 8px 0 0;
	margin-bottom: 0;
}

.user-page.pc-mode .pc-avatar {
	width: 80px !important;
	height: 80px !important;
}

.user-page.pc-mode .pc-username {
	font-size: 20px !important;
	margin-top: 12px !important;
}

.user-page.pc-mode .pc-sign {
	font-size: 14px !important;
	margin-top: 8px !important;
}

/* PC端统计容器样式 */
.user-page.pc-mode .pc-stats-container {
	padding: 20px !important;
	border-radius: 0;
	margin: 0;
	border-top: 1px solid #f1f3f4;
}

.user-page.pc-mode .pc-stat-number {
	font-size: 24px !important;
}

.user-page.pc-mode .pc-stat-label {
	font-size: 12px !important;
	margin-top: 6px !important;
}

/* PC端网格菜单样式 */
.user-page.pc-mode .pc-grid {
	padding: 15px 10px !important;
	border-radius: 6px;
	margin: 10px 0;
}

.user-page.pc-mode .pc-menu-item {
	padding: 20px 10px !important;
	border-radius: 6px;
	transition: all 0.2s ease;
	cursor: pointer;
}

.user-page.pc-mode .pc-menu-item:hover {
	background-color: #f8f9fa;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-page.pc-mode .pc-menu-image {
	width: 32px !important;
	height: 32px !important;
	margin-top: 8px !important;
}

.user-page.pc-mode .pc-menu-text {
	font-size: 12px !important;
	margin-top: 8px !important;
}

/* PC端分组标题栏样式 */
.user-page.pc-mode .pc-section-bar {
	border-radius: 6px;
	margin: 15px 0 5px 0 !important;
	padding: 12px 16px;
}

.user-page.pc-mode .pc-section-bar .action {
	font-size: 14px;
}

/* 响应式断点样式 */
@media (min-width: 1024px) {
	/* 用户页面整体布局 */
	.user-content {
		max-width: 900px;
		margin: 0 auto;
		padding: 15px;
		background-color: #ffffff;
		box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
		border-radius: 8px;
		margin-top: 15px;
	}

	/* 用户头部背景 */
	.UCenter-bg {
		height: 280px !important;
		border-radius: 8px 8px 0 0;
		margin-bottom: 0;
	}

	/* 用户头像 */
	.cu-avatar.xl {
		width: 80px !important;
		height: 80px !important;
	}

	/* 用户名 */
	.text-xl {
		font-size: 20px !important;
		margin-top: 12px !important;
	}

	/* 签名 */
	.sign {
		font-size: 14px !important;
		margin-top: 8px !important;
	}

	/* 统计数据容器 */
	.shadow-warp {
		padding: 20px !important;
		border-radius: 0;
		margin: 0;
		border-top: 1px solid #f1f3f4;
	}

	/* 统计数字 */
	.text-xxl {
		font-size: 24px !important;
	}

	/* 统计标签 */
	.margin-top-sm {
		font-size: 12px !important;
		margin-top: 6px !important;
	}

	/* 网格菜单 */
	.cu-list.grid {
		padding: 15px 10px !important;
		border-radius: 6px;
		margin: 10px 0;
	}

	.cu-list.grid > .cu-item {
		padding: 20px 10px !important;
		border-radius: 6px;
		transition: all 0.2s ease;
		cursor: pointer;
	}

	.cu-list.grid > .cu-item:hover {
		background-color: #f8f9fa;
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	/* 菜单图标 */
	.menu-image {
		width: 32px !important;
		height: 32px !important;
		margin-top: 8px !important;
	}

	/* 菜单文字 */
	.cu-list.grid > .cu-item text {
		font-size: 12px !important;
		margin-top: 8px !important;
	}

	/* 分组标题栏 */
	.cu-bar {
		border-radius: 6px;
		margin: 15px 0 5px 0 !important;
		padding: 12px 16px;
	}

	.cu-bar .action {
		font-size: 14px;
	}
}