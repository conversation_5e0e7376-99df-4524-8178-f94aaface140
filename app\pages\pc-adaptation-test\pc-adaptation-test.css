/* 移动端默认样式 (rpx单位) */
.test-container {
  padding: 20rpx;
  min-height: 100vh;
}

.test-section {
  margin-bottom: 40rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #007aff;
  padding-left: 20rpx;
}

.info-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.value.success {
  color: #28a745;
}

.value.pc-mode {
  color: #007aff;
}

.value.mobile-mode {
  color: #ff6b6b;
}

.style-demo {
  margin-top: 20rpx;
}

.demo-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  text-align: center;
  margin-bottom: 30rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.demo-content {
  font-size: 28rpx;
  display: block;
  margin-bottom: 30rpx;
  opacity: 0.9;
}

.demo-button {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  color: white;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.grid-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  border: 2rpx solid #f1f3f4;
  transition: all 0.3s ease;
}

.grid-item:active {
  transform: scale(0.95);
  background: #f8f9fa;
}

.grid-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.grid-text {
  font-size: 26rpx;
  color: #333;
  display: block;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.nav-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 30rpx;
  text-align: center;
}

.nav-btn:active {
  background: #0056b3;
  transform: scale(0.98);
}

/* PC端样式适配 (px单位) */
.pc-test-page.pc-mode {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.pc-test-page.pc-mode .test-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  margin-top: 20px;
}

.pc-test-page.pc-mode .test-section {
  margin-bottom: 30px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.pc-test-page.pc-mode .section-title {
  font-size: 18px;
  margin-bottom: 16px;
  border-left: 4px solid #007aff;
  padding-left: 12px;
}

.pc-test-page.pc-mode .info-card {
  border-radius: 8px;
  padding: 16px;
}

.pc-test-page.pc-mode .info-item {
  padding: 12px 0;
}

.pc-test-page.pc-mode .label,
.pc-test-page.pc-mode .value {
  font-size: 14px;
}

.pc-test-page.pc-mode .demo-card {
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
}

.pc-test-page.pc-mode .demo-title {
  font-size: 20px;
  margin-bottom: 12px;
}

.pc-test-page.pc-mode .demo-content {
  font-size: 14px;
  margin-bottom: 20px;
}

.pc-test-page.pc-mode .demo-button {
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pc-test-page.pc-mode .demo-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.pc-test-page.pc-mode .demo-grid {
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.pc-test-page.pc-mode .grid-item {
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
}

.pc-test-page.pc-mode .grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  background: #f8f9fa;
}

.pc-test-page.pc-mode .grid-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.pc-test-page.pc-mode .grid-text {
  font-size: 12px;
}

.pc-test-page.pc-mode .nav-buttons {
  flex-direction: row;
  gap: 16px;
}

.pc-test-page.pc-mode .nav-btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

.pc-test-page.pc-mode .nav-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

/* 响应式断点样式 */
@media (min-width: 1024px) {
  .test-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-top: 20px;
  }

  .test-section {
    margin-bottom: 30px;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
    border-left: 4px solid #007aff;
    padding-left: 12px;
  }

  .demo-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }

  .nav-buttons {
    flex-direction: row;
    gap: 16px;
  }

  .nav-btn {
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
  }

  .nav-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  }
}
