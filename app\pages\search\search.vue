<template>
	<view :class="pageClasses">
		<back :showBackText="false" :showBackLeft="false" customClass="bg-gradual-blue text-white" title="搜索"></back>
		<view class="flex flex-direction search-container" style="min-height: 100vh;">
			<view class="flex-sub">
				<view :class="formGroupClass" :style="isPC ? 'border-radius: 8px;margin: 15px;' : 'border-radius: 30rpx;margin: 10rpx;'">
					<textarea @input="onInputChange" placeholder="输入搜索关键字，搜课程（可以按照编码搜索），搜题目（支持拍照搜，语音搜）"
						:value="keyword"></textarea>
				</view>
				<view class="action-buttons-container" style="background-color: white;" v-if="!isAudioing && !appIsAudit">
					<view class="grid col-2 text-center" :style="isPC ? 'margin: 0 15px;' : 'margin: 0 10rpx;'">
						<view class="solid-bottom text-lg">
							<view :class="buttonGroupClass">
								<button :style="isPC ? 'margin: 0 8px;padding: 8px 12px;' : 'margin: 0 5rpx;padding: 0;'" class="cu-btn action-button"
									@tap="audioTap"><text class="cuIcon-voicefill"></text>语音</button>
								<button :style="isPC ? 'margin: 0 8px;padding: 8px 12px;' : 'margin: 0 5rpx;padding: 0;'" class="cu-btn action-button"
									@tap="chooseImageTap"><text class="cuIcon-camerafill"></text>拍照</button>
							</view>
						</view>
						<view class="solid-bottom text-lg">
							<view :class="buttonGroupClass">
								<button :style="isPC ? 'margin: 0 8px;padding: 8px 12px;' : 'margin: 0 5rpx;padding: 0;'" class="cu-btn action-button"
									@tap="clearAndPasteTap">清除并粘贴</button>
								<button :style="isPC ? 'margin: 0 8px;padding: 8px 12px;' : 'margin: 0 5rpx;padding: 0;'" class="cu-btn action-button"
									@tap="clearTap">清除</button>
							</view>
						</view>
					</view>
				</view>
				<view class="padding bg-white margin-top-xs" v-if="isAudioing" @tap="audioActionTap(false)">
					<view class="cu-progress radius striped active" style="height: 60rpx;">
						<view class="bg-green" style="width:100%;font-size: 30rpx;">录音中，点击停止</view>
					</view>
				</view>
				<view :class="isPC ? 'padding-sm pc-search-button' : 'padding-sm'">
					<view class=" flex flex-direction">
						<button :class="isPC ? 'cu-btn lg bg-gradual-blue shadow pc-search-btn' : 'cu-btn lg bg-gradual-blue shadow'" data-type="1" @tap="onSearchClick">
							<text class="cuIcon-search"></text>
							立即搜索</button>
					</view>
				</view>
				<view class="cu-bar bg-white solid-bottom search-dynamics-bar" :style="isPC ? 'margin-top: 15px;' : 'margin-top: 10rpx;'">
					<view class="action">
						<text class="cuIcon-title text-red"></text>
						搜索动态
					</view>
				</view>
				<view :class="isPC ? 'cu-list menu pc-search-list' : 'cu-list menu'">
					<view :class="isPC ? 'cu-item pc-search-item' : 'cu-item'" @tap="onClickQuestion(item.question_id)" v-for="(item, index) in recordList"
						:key="index">
						<view class="action">
							<text class="text-grey text-df">{{ index + 3 }}秒前</text>
							<view class="cu-avatar-group">
								<view class="cu-avatar sm round" :style="'background-image:url(' + item.avatar + ');'">
								</view>
							</view>
						</view>

						<view class="content">
							<text class="text-orange text-df">{{ item.nickname }}</text>
							<text class="text-gray text-df">搜索</text>
							<text class="text-blue text-df">{{ item.question_name }}</text>
						</view>
					</view>
				</view>
			</view>
			<adfootbanner></adfootbanner>
		</view>
	</view>
</template>
<style src="./search.css"></style>
<script src="./search.js"></script>