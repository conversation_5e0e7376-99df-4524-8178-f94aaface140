/* 移动端默认样式 (rpx单位) */
.questionAsk-layout {
	display: flex;
	flex-direction: column;
	padding: 30rpx;
	background: white;
	font-size: 30rpx;
}
.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	font-size: 10rpx;
	padding-bottom: 10rpx;
}
.bottom-layout {
	position: fixed;
	height: 6%;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	z-index: 1000;
}
.options-layout rich-text,.options-layout mp-html {
	margin-left: 30rpx;
}
.layout-result {
	margin: 15rpx 15rpx 0 15rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20rpx;
	font-size: 30rpx;
	color: #333;
}
.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}
/* PC端样式适配 (px单位) - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于1024px时应用PC端样式 */
/* PC端页面容器 */
.search-list-page.pc-mode {
	background-color: #f5f7fa;
	min-height: 100vh;
}
.search-list-page.pc-mode .page {
	max-width: 900px;
	margin: 0 auto;
	padding: 15px;
	background-color: #ffffff;
	box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	margin-top: 15px;
}
/* PC端搜索栏样式 */
.search-list-page.pc-mode .pc-search-bar {
	border-radius: 8px;
	padding: 12px 16px;
	margin: 10px 0;
}
.search-list-page.pc-mode .pc-search-form {
	border-radius: 6px !important;
	padding: 8px 12px;
	border: 1px solid #e1e5e9;
}
.search-list-page.pc-mode .pc-search-form input {
	font-size: 14px;
}
.search-list-page.pc-mode .pc-search-button {
	font-size: 14px !important;
	padding: 8px 16px !important;
	border-radius: 6px !important;
}
/* PC端导航标签样式 */
.search-list-page.pc-mode .pc-nav-tabs {
	border-radius: 6px;
	margin: 10px 0;
}
.search-list-page.pc-mode .pc-nav-item {
	font-size: 14px;
	padding: 12px 30px !important;
	border-radius: 4px;
	transition: all 0.2s ease;
}
.search-list-page.pc-mode .pc-nav-item:hover {
	background-color: #f8f9fa;
}
/* PC端问题布局样式 */
.search-list-page.pc-mode .questionAsk-layout {
	padding: 20px !important;
	font-size: 16px !important;
	border-radius: 6px;
	margin: 10px 0;
	border: 1px solid #f1f3f4;
}
.search-list-page.pc-mode .options-layout {
	font-size: 14px !important;
	padding-bottom: 15px !important;
	border-radius: 6px;
}
.search-list-page.pc-mode .layout-result {
	margin: 10px !important;
	padding: 15px !important;
	font-size: 16px !important;
	border-radius: 6px !important;
}
/* 响应式断点样式 */
@media (min-width: 1024px) {
	/* 搜索列表页面整体布局 */
.page {
		max-width: 900px;
		margin: 0 auto;
		padding: 15px;
		background-color: #ffffff;
		box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
		border-radius: 8px;
		margin-top: 15px;
}

	/* 搜索栏 */
.cu-bar.search {
		border-radius: 8px;
		padding: 12px 16px;
		margin: 10px 0;
}
.search-form {
		border-radius: 6px !important;
		padding: 8px 12px;
		border: 1px solid #e1e5e9;
}
.search-form input {
		font-size: 14px;
}
.cu-btn {
		font-size: 14px !important;
		padding: 8px 16px !important;
		border-radius: 6px !important;
}

	/* 导航标签 */
.nav {
		border-radius: 6px;
		margin: 10px 0;
}
.cu-item {
		font-size: 14px;
		padding: 12px 30px !important;
		border-radius: 4px;
		transition: all 0.2s ease;
}
.cu-item:hover {
		background-color: #f8f9fa;
}

	/* 问题布局 */
.questionAsk-layout {
		padding: 20px !important;
		font-size: 16px !important;
		border-radius: 6px;
		margin: 10px 0;
		border: 1px solid #f1f3f4;
}
.options-layout {
		font-size: 14px !important;
		padding-bottom: 15px !important;
		border-radius: 6px;
}
.layout-result {
		margin: 10px !important;
		padding: 15px !important;
		font-size: 16px !important;
		border-radius: 6px !important;
}

	/* 底部布局 */
.bottom-layout {
		height: 60px !important;
		padding: 0 20px;
}
}
.explain-layout {
	padding: 30rpx;
	background: white;
}
.explain-label {
	font-size: 24rpx;
	font-weight: bold;
	color: #666;
}
.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 30rpx;
	color: #333;
}
.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}
.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}
.explain-answer .answer {
	color: #333;
}
.explain-layout .explain-text {
	font-size: 30rpx;
	color: #333;
}
