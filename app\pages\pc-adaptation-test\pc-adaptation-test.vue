<template>
  <view :class="pageClasses">
    <back :showBackText="false" customClass="bg-gradual-blue text-white" title="PC端适配测试"></back>
    
    <view class="test-container">
      <view class="test-section">
        <view class="section-title">环境检测结果</view>
        <view class="info-card">
          <view class="info-item">
            <text class="label">是否PC端：</text>
            <text :class="isPC ? 'value success' : 'value'">{{ isPC ? '是' : '否' }}</text>
          </view>
          <view class="info-item">
            <text class="label">屏幕类型：</text>
            <text class="value">{{ screenType }}</text>
          </view>
          <view class="info-item">
            <text class="label">窗口宽度：</text>
            <text class="value">{{ windowWidth }}px</text>
          </view>
          <view class="info-item">
            <text class="label">当前模式：</text>
            <text :class="isPC ? 'value pc-mode' : 'value mobile-mode'">{{ isPC ? 'PC模式' : '移动端模式' }}</text>
          </view>
        </view>
      </view>

      <view class="test-section">
        <view class="section-title">样式测试</view>
        <view class="style-demo">
          <view class="demo-card">
            <text class="demo-title">响应式卡片</text>
            <text class="demo-content">这个卡片在PC端和移动端有不同的样式</text>
            <button class="demo-button" @click="showToast">测试按钮</button>
          </view>
          
          <view class="demo-grid">
            <view class="grid-item" v-for="(item, index) in testItems" :key="index">
              <image class="grid-icon" :src="item.icon" mode="aspectFit"></image>
              <text class="grid-text">{{ item.name }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="test-section">
        <view class="section-title">页面导航测试</view>
        <view class="nav-buttons">
          <button class="nav-btn" @click="goToSearch">搜索页面</button>
          <button class="nav-btn" @click="goToUser">用户页面</button>
          <button class="nav-btn" @click="goToSearchList">搜索列表</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
let that = null;
let app = getApp();

export default {
  data() {
    return {
      isPC: false,
      screenType: 'mobile',
      windowWidth: 0,
      testItems: [
        { name: '测试1', icon: '/static/img/book.png' },
        { name: '测试2', icon: '/static/img/email.png' },
        { name: '测试3', icon: '/static/img/phone.png' },
        { name: '测试4', icon: '/static/img/password.png' }
      ]
    };
  },
  computed: {
    pageClasses() {
      return 'pc-test-page' + (this.isPC ? ' pc-mode' : '');
    }
  },
  onLoad() {
    that = this;
    that.detectPC();
  },
  methods: {
    detectPC() {
      try {
        const systemInfo = uni.getSystemInfoSync();
        that.windowWidth = systemInfo.windowWidth;
        that.isPC = systemInfo.windowWidth >= 1024;
        that.screenType = systemInfo.windowWidth >= 1024 ? 'desktop' : 'mobile';
        
        console.log('PC适配测试页面检测结果:', {
          windowWidth: that.windowWidth,
          isPC: that.isPC,
          screenType: that.screenType
        });
      } catch (error) {
        console.error('PC端检测失败:', error);
        that.isPC = false;
        that.screenType = 'mobile';
        that.windowWidth = 0;
      }
    },
    showToast() {
      uni.showToast({
        title: this.isPC ? 'PC端点击' : '移动端点击',
        icon: 'success'
      });
    },
    goToSearch() {
      uni.navigateTo({
        url: '/pages/search/search'
      });
    },
    goToUser() {
      uni.navigateTo({
        url: '/pages/user/user'
      });
    },
    goToSearchList() {
      uni.navigateTo({
        url: '/pages/search/list?keyword=测试'
      });
    }
  }
};
</script>

<style src="./pc-adaptation-test.css"></style>
