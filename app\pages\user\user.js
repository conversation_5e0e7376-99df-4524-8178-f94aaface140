let that = null;
let app = getApp();
export default {
	data() {
		return {
			load: false,
			user: [],
			appPlatform: app.globalData.appPlatform,
			// PC端适配相关数据
			isPC: false,
			isPCWeixin: false,
			screenType: 'mobile',
			paths: {
				"info": "/pages/user/info/info",
				"about": "/pages/user/about/about",
				"switch": "/pages/user/switch/switch",
				'promote': "/pages/user/promote/promote",
				"ranking": "/pages/user/ranking/ranking",
				"customer": "/pages/user/customer/customer",
				"score": "/pages/user/sign/sign",
				"rechargeMember": "/pages/user/vip/vip",
				"wechat": "/pages/user/login/bindopenid?from=2",
				"email": "/pages/user/login/bindemail",
				"mobile": "/pages/user/info/edit",
				"opinion": "/pages/user/opinion/opinion",
				"password": "/pages/user/set/password",
				"myCourse": "/pages/user/course/course",
				"myCollect": "/pages/practice/coll/coll",
				"myErrorQuestion": "/pages/practice/error/error",
			},
			appIsAudit: false,
			isIosVirtualPay: true,
			showOnlineServiceNotice: false
		};
	},
	computed: {
		pageClasses() {
			return 'user-page' + (this.isPC ? ' pc-mode' : '');
		},
		gridClasses() {
			return this.isPC ? 'cu-list grid col-5 no-border pc-grid' : 'cu-list grid col-5 no-border';
		}
	},
	onLoad() {
		that = this;
		that.detectPC();
		app.globalData.showShareMenu();
	},
	onShow() {
		that.getUserInfo();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * PC端检测
		 */
		detectPC() {
			try {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				const windowWidth = systemInfo.windowWidth;

				// 简单判断：宽度大于1024认为是PC端
				that.isPC = windowWidth >= 1024;
				that.screenType = windowWidth >= 1024 ? 'desktop' : 'mobile';

				console.log('用户页面PC端检测结果:', {
					windowWidth: windowWidth,
					isPC: that.isPC,
					screenType: that.screenType
				});
			} catch (error) {
				console.error('用户页面PC端检测失败:', error);
				that.isPC = false;
				that.screenType = 'mobile';
			}
		},

		getUserInfo() {
			app.globalData.server
				.getRequest('user/info', {})
				.then((e) => {
					app.globalData.config.storage.setUserInfoData(e.data);
					that.setData({
						user: e.data,
						appIsAudit: app.globalData.checkAppIsAudit(),		
						load: true
					});
					that.isIosVirtualPay = app.globalData.checkIsIosVirtualPay();
				})
				.catch((e) => {
					console.log(e);
				});
		},
		copyTap(data) {
			console.log(data);
			uni.setClipboardData({
				data: data.toString(),
				success() {
					app.showToast('复制成功');
				},
				fail(res) {
					console.log(res);
				}
			});
		},
		menuTap(options) {
			let url = options.currentTarget.dataset.url;
			console.log(url)
			uni.navigateTo({
				url: url
			});
		},
		showOnlineServiceTap() {
			that.showOnlineServiceNotice = true;
		},
	}
};