<template>
  <view :class="pageClasses">
    <view v-if="load" class="user-content">
      <view :class="isPC ? 'UCenter-bg pc-user-header' : 'UCenter-bg'" @tap="menuTap" :data-url='paths.info' :style="'background-image: url('+user.pageConfig.background_image+');background-size: cover;'">
        <view :class="isPC ? 'cu-avatar xl round pc-avatar' : 'cu-avatar xl round'" :style="'background-image:url('+user.avatar+');'">
          <view v-if="user.is_vip==1" class="cu-tag badge bg-yellow cuIcon-vip"></view>
        </view>
        <view :class="isPC ? 'text-xl margin-top pc-username' : 'text-xl margin-top'">
          <text>你好，{{user.nickname}}</text>
        </view>
        <view :class="isPC ? 'margin-top-sm pc-sign' : 'margin-top-sm'">
          <text class="sign">相信美好的事情即将发生</text>
        </view>
        <image class="gif-wave" mode="scaleToFill"
               src="https://learnfile.20230611.cn/learnAppClient/cb/de/cbde6f5e83388a096d28fab7339d56fd.gif">
        </image>
      </view>
      <view :class="isPC ? 'padding flex text-center text-grey bg-white shadow-warp pc-stats-container' : 'padding flex text-center text-grey bg-white shadow-warp'">
        <view class="flex flex-sub flex-direction solid-right" @tap="copyTap(user.id)">
          <view :class="isPC ? 'text-xxl text-orange pc-stat-number' : 'text-xxl text-orange'">{{ user.id }}</view>
          <view :class="isPC ? 'margin-top-sm pc-stat-label' : 'margin-top-sm'">
            <text class="cuIcon-cuIcon"></text> 编号
          </view>
        </view>
        <view class="flex flex-sub flex-direction solid-right" @tap="menuTap" :data-url="paths.rechargeMember">
          <view :class="isPC ? 'text-xxl text-blue pc-stat-number' : 'text-xxl text-blue'">{{ user.vip_name }}</view>
          <view :class="isPC ? 'margin-top-sm pc-stat-label' : 'margin-top-sm'">
            <text class="cuIcon-peoplefill"></text> 级别
          </view>
        </view>
        <view class="flex flex-sub flex-direction" @tap="menuTap" :data-url="paths.score">
          <view :class="isPC ? 'text-xxl text-green pc-stat-number' : 'text-xxl text-green'">{{ user.score }}</view>
          <view :class="isPC ? 'margin-top-sm pc-stat-label' : 'margin-top-sm'">
            <text class="cuIcon-rechargefill"></text> 积分
          </view>
        </view>
      </view>
      <view class="cu-bar bg-white solid-bottom margin-top-xs">
        <view class="action">
          <text class=" text-orange"></text> 账户管理
        </view>
      </view>

      <view :class="gridClasses">
        <!--  #ifdef   MP-WEIXIN -->
        <view :class="isPC ? 'cu-item pc-menu-item' : 'cu-item'" @tap="menuTap" :data-url="paths.wechat" v-if="!user.openid">
          <image :style="isPC ? 'width: 100%;' : 'width: 100%;'" :class="isPC ? 'menu-image pc-menu-image' : 'menu-image'" mode="aspectFit" src="/static/img/wechat.png">
          </image>
          <text :class="isPC ? 'pc-menu-text' : ''">绑定微信</text>
        </view>
        <!--  #endif -->
        <view v-if="!appIsAudit && !user.email" :class="isPC ? 'cu-item pc-menu-item' : 'cu-item'" @tap="menuTap" :data-url="paths.email">
          <image :style="isPC ? 'width: 100%;' : 'width: 100%;'" :class="isPC ? 'menu-image pc-menu-image' : 'menu-image'" mode="aspectFit" src="/static/img/email.png">
          </image>
          <text :class="isPC ? 'pc-menu-text' : ''">绑定邮箱</text>
        </view>
        <view v-if="!appIsAudit && !user.mobile" :class="isPC ? 'cu-item pc-menu-item' : 'cu-item'" @tap="menuTap" :data-url="paths.mobile">
          <image :style="isPC ? 'width: 100%;' : 'width: 100%;'" :class="isPC ? 'menu-image pc-menu-image' : 'menu-image'" mode="aspectFit" src="/static/img/phone.png">
          </image>
          <text :class="isPC ? 'pc-menu-text' : ''">绑定手机</text>
        </view>
        <view :class="isPC ? 'cu-item pc-menu-item' : 'cu-item'" @tap="menuTap" :data-url="paths.password">
          <image :style="isPC ? 'width: 100%;' : 'width: 100%;'" :class="isPC ? 'menu-image pc-menu-image' : 'menu-image'" mode="aspectFit" src="/static/img/password.png">
          </image>
          <text :class="isPC ? 'pc-menu-text' : ''">设置密码</text>
        </view>
        <view :class="isPC ? 'cu-item pc-menu-item' : 'cu-item'" @tap="menuTap" :data-url="paths.info">
          <image :style="isPC ? 'width: 100%;' : 'width: 100%;'" :class="isPC ? 'menu-image pc-menu-image' : 'menu-image'" mode="aspectFit" src="/static/img/user-male-circle.png">
          </image>
          <text :class="isPC ? 'pc-menu-text' : ''">个人资料</text>
        </view>
      </view>
      <view :class="isPC ? 'cu-bar bg-white solid-bottom margin-top-xs pc-section-bar' : 'cu-bar bg-white solid-bottom margin-top-xs'">
        <view class="action">
          <text class="text-orange "></text> 学习管理
        </view>
      </view>
      <view :class="gridClasses">
        <view :class="isPC ? 'cu-item pc-menu-item' : 'cu-item'" @tap="menuTap" :data-url="paths.myCourse">
          <image :style="isPC ? 'width: 100%;' : 'width: 100%;'" :class="isPC ? 'menu-image pc-menu-image' : 'menu-image'" mode="aspectFit" src="/static/img/book.png">
          </image>
          <text>我的题库</text>
        </view>
        <view class="cu-item" @tap="menuTap" :data-url="paths.myErrorQuestion">
          <image style="width: 100%;" class="menu-image" mode="aspectFit" src="/static/img/error.png">
          </image>
          <text>我的错题</text>
        </view>
        <view class="cu-item" @tap="menuTap" :data-url="paths.myCollect">
          <image style="width: 100%;" class="menu-image" mode="aspectFit" src="/static/img/bookmark.png">
          </image>
          <text>我的收藏</text>
        </view>
      </view>
      <view class="cu-bar bg-white solid-bottom margin-top-xs">
        <view class="action">
          <text class="text-orange "></text> 我的服务
        </view>
      </view>
      <view class="cu-list grid col-5 no-border">
        <view class="cu-item" @tap="menuTap" :data-url="paths.about" v-if="!appIsAudit">
          <image style="width: 100%;" class="menu-image" mode="aspectFit" src="/static/img/about.png">
          </image>
          <text>关于我们</text>
        </view>
        <view class="cu-item" v-if="(appPlatform==20 || appPlatform==21)"  @tap="showOnlineServiceTap()">
          <image style="width: 100%;" class="menu-image" mode="aspectFit" src="/static/img/customer-support.png">
          </image>
          <text>在线客服</text>
        </view>
        <view class="cu-item" @tap="menuTap" :data-url="paths.opinion">
          <image style="width: 100%;" class="menu-image" mode="aspectFit" src="/static/img/feeback.png">
          </image>
          <text>意见反馈</text>
        </view>
        <view class="cu-item" @tap="menuTap" :data-url="paths.promote" v-if="user.is_agent==1">
          <image style="width: 100%;" class="menu-image" mode="aspectFit" src="/static/img/share.png">
          </image>
          <text>推广中心</text>
        </view>
      </view>
    </view>

    <confirm title="接入提醒" content="在线客服属于兼职，回复可能会不及时，请先留言，最晚会在12小时内回复您。" :status.sync="showOnlineServiceNotice"
             confirmText="知道啦，我要接入客服" confirmTextClass="cuIcon-servicefill" confirmButtonOpenType="contact">
    </confirm>

  </view>
  </view>
</template>
<style src="./user.css"></style>
<script src="./user.js"></script>