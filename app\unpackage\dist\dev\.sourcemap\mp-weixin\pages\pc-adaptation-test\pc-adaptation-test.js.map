{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/pc-adaptation-test/pc-adaptation-test.vue?6958", "webpack:///D:/桌面/thinker/app/pages/pc-adaptation-test/pc-adaptation-test.vue?fbcc", "webpack:///D:/桌面/thinker/app/pages/pc-adaptation-test/pc-adaptation-test.vue?82aa", "webpack:///D:/桌面/thinker/app/pages/pc-adaptation-test/pc-adaptation-test.vue?ff37", "uni-app:///pages/pc-adaptation-test/pc-adaptation-test.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isPC", "screenType", "windowWidth", "testItems", "name", "icon", "computed", "pageClasses", "onLoad", "that", "methods", "detectPC", "console", "showToast", "uni", "title", "goToSearch", "url", "goToUser", "goToSearchList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AACsE;AACL;AACa;;;AAG9E;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,oqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0D1rB;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC,YACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;IACAA;EACA;EACAC;IACAC;MACA;QACA;QACAF;QACAA;QACAA;QAEAG;UACAV;UACAF;UACAC;QACA;MACA;QACAW;QACAH;QACAA;QACAA;MACA;IACA;IACAI;MACAC;QACAC;QACAV;MACA;IACA;IACAW;MACAF;QACAG;MACA;IACA;IACAC;MACAJ;QACAG;MACA;IACA;IACAE;MACAL;QACAG;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/pc-adaptation-test/pc-adaptation-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pc-adaptation-test/pc-adaptation-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-adaptation-test.vue?vue&type=template&id=3ccfddf4&\"\nvar renderjs\nimport script from \"./pc-adaptation-test.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-adaptation-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-adaptation-test.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pc-adaptation-test/pc-adaptation-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adaptation-test.vue?vue&type=template&id=3ccfddf4&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adaptation-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adaptation-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view :class=\"pageClasses\">\n    <back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"PC端适配测试\"></back>\n    \n    <view class=\"test-container\">\n      <view class=\"test-section\">\n        <view class=\"section-title\">环境检测结果</view>\n        <view class=\"info-card\">\n          <view class=\"info-item\">\n            <text class=\"label\">是否PC端：</text>\n            <text :class=\"isPC ? 'value success' : 'value'\">{{ isPC ? '是' : '否' }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">屏幕类型：</text>\n            <text class=\"value\">{{ screenType }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">窗口宽度：</text>\n            <text class=\"value\">{{ windowWidth }}px</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"label\">当前模式：</text>\n            <text :class=\"isPC ? 'value pc-mode' : 'value mobile-mode'\">{{ isPC ? 'PC模式' : '移动端模式' }}</text>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"test-section\">\n        <view class=\"section-title\">样式测试</view>\n        <view class=\"style-demo\">\n          <view class=\"demo-card\">\n            <text class=\"demo-title\">响应式卡片</text>\n            <text class=\"demo-content\">这个卡片在PC端和移动端有不同的样式</text>\n            <button class=\"demo-button\" @click=\"showToast\">测试按钮</button>\n          </view>\n          \n          <view class=\"demo-grid\">\n            <view class=\"grid-item\" v-for=\"(item, index) in testItems\" :key=\"index\">\n              <image class=\"grid-icon\" :src=\"item.icon\" mode=\"aspectFit\"></image>\n              <text class=\"grid-text\">{{ item.name }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"test-section\">\n        <view class=\"section-title\">页面导航测试</view>\n        <view class=\"nav-buttons\">\n          <button class=\"nav-btn\" @click=\"goToSearch\">搜索页面</button>\n          <button class=\"nav-btn\" @click=\"goToUser\">用户页面</button>\n          <button class=\"nav-btn\" @click=\"goToSearchList\">搜索列表</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nlet that = null;\nlet app = getApp();\n\nexport default {\n  data() {\n    return {\n      isPC: false,\n      screenType: 'mobile',\n      windowWidth: 0,\n      testItems: [\n        { name: '测试1', icon: '/static/img/book.png' },\n        { name: '测试2', icon: '/static/img/email.png' },\n        { name: '测试3', icon: '/static/img/phone.png' },\n        { name: '测试4', icon: '/static/img/password.png' }\n      ]\n    };\n  },\n  computed: {\n    pageClasses() {\n      return 'pc-test-page' + (this.isPC ? ' pc-mode' : '');\n    }\n  },\n  onLoad() {\n    that = this;\n    that.detectPC();\n  },\n  methods: {\n    detectPC() {\n      try {\n        const systemInfo = uni.getSystemInfoSync();\n        that.windowWidth = systemInfo.windowWidth;\n        that.isPC = systemInfo.windowWidth >= 1024;\n        that.screenType = systemInfo.windowWidth >= 1024 ? 'desktop' : 'mobile';\n        \n        console.log('PC适配测试页面检测结果:', {\n          windowWidth: that.windowWidth,\n          isPC: that.isPC,\n          screenType: that.screenType\n        });\n      } catch (error) {\n        console.error('PC端检测失败:', error);\n        that.isPC = false;\n        that.screenType = 'mobile';\n        that.windowWidth = 0;\n      }\n    },\n    showToast() {\n      uni.showToast({\n        title: this.isPC ? 'PC端点击' : '移动端点击',\n        icon: 'success'\n      });\n    },\n    goToSearch() {\n      uni.navigateTo({\n        url: '/pages/search/search'\n      });\n    },\n    goToUser() {\n      uni.navigateTo({\n        url: '/pages/user/user'\n      });\n    },\n    goToSearchList() {\n      uni.navigateTo({\n        url: '/pages/search/list?keyword=测试'\n      });\n    }\n  }\n};\n</script>\n\n<style src=\"./pc-adaptation-test.css\"></style>\n"], "sourceRoot": ""}