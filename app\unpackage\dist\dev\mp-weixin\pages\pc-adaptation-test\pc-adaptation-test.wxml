<view class="{{[pageClasses]}}"><back vue-id="516a90c0-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="PC端适配测试" bind:__l="__l"></back><view class="test-container"><view class="test-section"><view class="section-title">环境检测结果</view><view class="info-card"><view class="info-item"><text class="label">是否PC端：</text><text class="{{[isPC?'value success':'value']}}">{{isPC?'是':'否'}}</text></view><view class="info-item"><text class="label">屏幕类型：</text><text class="value">{{screenType}}</text></view><view class="info-item"><text class="label">窗口宽度：</text><text class="value">{{windowWidth+"px"}}</text></view><view class="info-item"><text class="label">当前模式：</text><text class="{{[isPC?'value pc-mode':'value mobile-mode']}}">{{isPC?'PC模式':'移动端模式'}}</text></view></view></view><view class="test-section"><view class="section-title">样式测试</view><view class="style-demo"><view class="demo-card"><text class="demo-title">响应式卡片</text><text class="demo-content">这个卡片在PC端和移动端有不同的样式</text><button data-event-opts="{{[['tap',[['showToast',['$event']]]]]}}" class="demo-button" bindtap="__e">测试按钮</button></view><view class="demo-grid"><block wx:for="{{testItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="grid-item"><image class="grid-icon" src="{{item.icon}}" mode="aspectFit"></image><text class="grid-text">{{item.name}}</text></view></block></view></view></view><view class="test-section"><view class="section-title">页面导航测试</view><view class="nav-buttons"><button data-event-opts="{{[['tap',[['goToSearch',['$event']]]]]}}" class="nav-btn" bindtap="__e">搜索页面</button><button data-event-opts="{{[['tap',[['goToUser',['$event']]]]]}}" class="nav-btn" bindtap="__e">用户页面</button><button data-event-opts="{{[['tap',[['goToSearchList',['$event']]]]]}}" class="nav-btn" bindtap="__e">搜索列表</button></view></view></view></view>