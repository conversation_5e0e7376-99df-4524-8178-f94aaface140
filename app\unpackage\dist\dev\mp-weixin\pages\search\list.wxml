<view class="{{[pageClasses]}}"><view class="nav"><back vue-id="7f16836c-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="搜索" bind:__l="__l"></back><view class="nav"><view class="{{[searchBarClass]}}"><view class="{{[isPC?'search-form round pc-search-form':'search-form round']}}"><text class="cuIcon-search"></text><input type="text" data-type="1" placeholder="请输入搜索关键字" confirm-type="search" data-event-opts="{{[['input',[['onInputChange',['$event']]]],['confirm',[['onSearchClick',['$event']]]]]}}" value="{{keyword}}" bindinput="__e" bindconfirm="__e"/></view><view data-event-opts="{{[['tap',[['onSearchClick',['$event']]]]]}}" class="action" bindtap="__e"><button class="{{[isPC?'cu-btn bg-gradual-blue shadow-blur round pc-search-button':'cu-btn bg-gradual-blue shadow-blur round']}}">搜索</button></view></view><view class="{{[isPC?'bg-white nav text-center pc-nav-tabs':'bg-white nav text-center']}}" scroll-x="{{true}}"><block wx:for="{{selectModel}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{index==selectModelIndex?'text-blue cur':''}}" style="{{(isPC?'padding: 0 30px':'padding: 0 45rpx')}}" data-id="{{index}}" data-event-opts="{{[['tap',[['selectModelTap',['$event']]]]]}}" bindtap="__e">{{''+item.name+''}}</view></block></view></view></view><block wx:if="{{$root.g0}}"><view class="cu-bar flex bg-white"><view class="action"><text class="cuIcon-discover text-green"></text>检索结果</view></view></block><block wx:if="{{$root.g1}}"><scroll-view style="{{'height:'+(pageHeight+'px')+';'}}" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]]]}}" bindscrolltolower="__e"><view class="{{['cu-list',selectModelIndex==0?'menu':'']}}"><block wx:for="{{dataList}}" wx:for-item="data" wx:for-index="index" wx:key="index"><view class="{{['cu-item','',selectModelIndex==0?'':'margin-bottom-xs margin-top']}}" data-id="{{data.id}}" data-event-opts="{{[['tap',[['onClickItem',['$event']]]]]}}" bindtap="__e"><block wx:if="{{selectModelIndex==0}}"><view class="course"><view class="content"><text class="cuIcon-read text-red"></text><text class="text-blue text-df">{{data.name}}</text></view></view></block><block wx:if="{{selectModelIndex==1}}"><view class="question"><view class="bg-white questionAsk-layout" style="padding-bottom:0;"><view class="cu-capsule"><view class="cu-tag bg-blue">{{"第"+(index+1)+"题"}}</view><view class="cu-tag line-blue">{{data.questionTypeName}}</view></view></view><view class="questionAsk-layout"><view><rich-text nodes="{{data.questionAsk}}" preview-img="true"></rich-text></view></view><block wx:if="{{data.questionType==1||data.questionType==2||data.questionType==3}}"><view class="options-layout"><block wx:if="{{data.answerOption.A}}"><view class="{{[(data.correct_A?'layout-result-correct':'')+' layout-result']}}"><text>A</text><rich-text nodes="{{data.answerOption.A}}"></rich-text></view></block><block wx:if="{{data.answerOption.B}}"><view class="{{[(data.correct_B?'layout-result-correct':'')+' layout-result']}}"><text>B</text><rich-text nodes="{{data.answerOption.B}}"></rich-text></view></block><block wx:if="{{data.answerOption.C}}"><view class="{{[(data.correct_C?'layout-result-correct':'')+' layout-result']}}"><text>C</text><rich-text nodes="{{data.answerOption.C}}"></rich-text></view></block><block wx:if="{{data.answerOption.D}}"><view class="{{[(data.correct_D?'layout-result-correct':'')+' layout-result']}}"><text>D</text><rich-text nodes="{{data.answerOption.D}}"></rich-text></view></block><block wx:if="{{data.answerOption.E}}"><view class="{{[(data.correct_E?'layout-result-correct':'')+' layout-result']}}"><text>E</text><rich-text nodes="{{data.answerOption.E}}"></rich-text></view></block><block wx:if="{{data.answerOption.F}}"><view class="{{[(data.correct_F?'layout-result-correct':'')+' layout-result']}}"><text>F</text><rich-text nodes="{{data.answerOption.F}}"></rich-text></view></block></view></block><view class="explain-layout"><view style="display:flex;"><view class="explain-answer"><block wx:if="{{data.questionType==1||data.questionType==2||data.questionType==3}}"><view>参考答案</view></block><rich-text class="{{[data.questionType==1||data.questionType==2||data.questionType==3?'correct':'answer']}}" nodes="{{data.correctOption}}"></rich-text></view></view><block wx:if="{{data.explanation}}"><view class="explain-label" style="margin-top:40rpx;padding-bottom:30rpx;">解析</view></block><block wx:if="{{data.explanation}}"><rich-text class="explain-text" nodes="{{data.explanation}}" preview-img="true"></rich-text></block></view></view></block></view></block></view></scroll-view></block><block wx:if="{{$root.g2}}"><empty vue-id="7f16836c-2" info="未搜索到数据" bind:__l="__l"></empty></block><block wx:if="{{$root.g3>0}}"><adbanner vue-id="7f16836c-3" unitId="adunit-1fb0622d11b3c262" bind:__l="__l"></adbanner></block></view>