/* 绑定容器 */
.bind-container {
  padding: 30rpx;
}

/* 顶部说明区域 */
.header-info {
  text-align: center;
  margin-bottom: 50rpx;
  padding: 20rpx 0;
}

.title-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.desc-text {
  font-size: 28rpx;
  color: #666;
}

/* 分隔�?*/
.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider .line {
  flex: 1;
  height: 1rpx;
  background-color: #eee;
}

.divider .text {
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #999;
}

/* 快捷绑定区域 */
.quick-bind-section, .manual-bind-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 按钮样式 */
.cu-btn.radius {
  border-radius: 12rpx;
}

.cu-btn.lg {
  height: 90rpx;
  font-size: 32rpx;
}

/* 协议部分 */
.agreement-section {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 帮助信息区域 */
.help-section {
  margin-top: 30rpx;
}

/* 表单组样式优�?*/
.cu-form-group {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border: none;
}

.cu-form-group .title {
  font-size: 28rpx;
  color: #333;
}

.cu-form-group input {
  font-size: 28rpx;
}