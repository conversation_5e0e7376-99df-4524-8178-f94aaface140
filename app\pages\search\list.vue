<template>
	<view :class="pageClasses">
		<view class="nav">
			<back :showBackText="false" customClass="bg-gradual-blue text-white" title="搜索"></back>
			<view class="nav">
				<view :class="searchBarClass">
					<view :class="isPC ? 'search-form round pc-search-form' : 'search-form round'">
						<text class="cuIcon-search"></text>
						<input type="text" @input="onInputChange" data-type="1" @confirm="onSearchClick"
							placeholder="请输入搜索关键字" confirm-type="search" :value="keyword" />
					</view>
					<view class="action" @tap="onSearchClick">
						<button :class="isPC ? 'cu-btn bg-gradual-blue shadow-blur round pc-search-button' : 'cu-btn bg-gradual-blue shadow-blur round'">搜索</button>
					</view>
				</view>
				<view :scroll-x="true" :class="isPC ? 'bg-white nav text-center pc-nav-tabs' : 'bg-white nav text-center'">
					<view @tap="selectModelTap" :style="isPC ? 'padding: 0 30px' : 'padding: 0 45rpx'"
						:class="[
							isPC ? 'cu-item pc-nav-item' : 'cu-item',
							index==selectModelIndex ? 'text-blue cur' : ''
						]" v-for="(item,index) in selectModel"
						:key="index" :data-id="index">
						{{item.name}}
					</view>
				</view>
			</view>
		</view>
		<view class="cu-bar flex bg-white" v-if="(dataList.length > 0 && isLoad )">
			<view class="action">
				<text class="cuIcon-discover text-green"></text>
				检索结果
			</view>
		</view>
		<scroll-view scroll-y :style="{height:pageHeight+'px'}" v-if="dataList.length > 0 && isLoad"
			@scrolltolower="loadMore">
			<view class="cu-list" :class="selectModelIndex==0 ? 'menu':''">
				<view class="cu-item " :class="selectModelIndex==0 ? '':'margin-bottom-xs margin-top'"
					@tap="onClickItem" :data-id="data.id" v-for="(data, index) in dataList" :key="index">

					<!--搜课程-->
					<view class="course " v-if="selectModelIndex==0">
						<view class="content">
							<text class="cuIcon-read text-red"></text>
							<text class="text-blue text-df">{{ data.name }}</text>
						</view>
					</view>

					<!--搜题-->
					<view class="question" v-if="selectModelIndex==1">
						<view class="bg-white questionAsk-layout" style="padding-bottom: 0;">
							<view class="cu-capsule">
								<view class="cu-tag bg-blue">第{{index+1}}题</view>
								<view class="cu-tag line-blue">{{data.questionTypeName}}</view>
							</view>
						</view>

						<view class="questionAsk-layout">
							<view>
								<rich-text :nodes="data.questionAsk" preview-img="true" />
							</view>
						</view>

						<!--选择题选项-->
						<view class="options-layout"
							v-if="data.questionType == 1 || data.questionType == 2 || data.questionType == 3">
							<view :class="(data.correct_A ? 'layout-result-correct' : '') + ' layout-result'"
								v-if="data.answerOption.A">
								<text>A</text>
								<rich-text :nodes="data.answerOption.A" />
							</view>

							<view :class="(data.correct_B ? 'layout-result-correct' : '') + ' layout-result'"
								v-if="data.answerOption.B">
								<text>B</text>
								<rich-text :nodes="data.answerOption.B" />
							</view>

							<view :class="(data.correct_C ? 'layout-result-correct' : '') + ' layout-result'"
								v-if="data.answerOption.C">
								<text>C</text>
								<rich-text :nodes="data.answerOption.C" />
							</view>

							<view :class="(data.correct_D ? 'layout-result-correct' : '') + ' layout-result'"
								v-if="data.answerOption.D">
								<text>D</text>
								<rich-text :nodes="data.answerOption.D" />
							</view>

							<view :class="(data.correct_E ? 'layout-result-correct' : '') + ' layout-result'"
								v-if="data.answerOption.E">
								<text>E</text>
								<rich-text :nodes="data.answerOption.E" />
							</view>

							<view :class="(data.correct_F ? 'layout-result-correct' : '') + ' layout-result'"
								v-if="data.answerOption.F">
								<text>F</text>
								<rich-text :nodes="data.answerOption.F" />
							</view>
						</view>

						<!--正确答案和选项-->
						<view class="explain-layout ">
							<view style="display: flex">
								<view class="explain-answer">
									<view
										v-if="data.questionType == 1 || data.questionType == 2 || data.questionType == 3">
										参考答案</view>
									<rich-text
										:class="data.questionType == 1 || data.questionType == 2 || data.questionType == 3 ? 'correct' : 'answer'"
										:nodes="data.correctOption"></rich-text>
								</view>
							</view>
							<view class="explain-label" style="margin-top: 40rpx; padding-bottom: 30rpx"
								v-if="data.explanation">解析</view>
							<rich-text class="explain-text" :nodes="data.explanation" v-if="data.explanation"
								preview-img="true" />
							</rich-text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<empty v-if="dataList.length == 0 && isLoad" info="未搜索到数据"></empty>
		<adbanner v-if="dataList.length > 0" unitId="adunit-1fb0622d11b3c262"></adbanner>
	</view>
</template>
<style src="./list.css"></style>
<script src="./list.js"></script>