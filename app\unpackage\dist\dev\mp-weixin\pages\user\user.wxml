<view class="{{[pageClasses]}}"><block wx:if="{{load}}"><view class="user-content"><view class="{{[isPC?'UCenter-bg pc-user-header':'UCenter-bg']}}" style="{{('background-image: url('+user.pageConfig.background_image+');background-size: cover;')}}" data-url="{{paths.info}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="{{[isPC?'cu-avatar xl round pc-avatar':'cu-avatar xl round']}}" style="{{('background-image:url('+user.avatar+');')}}"><block wx:if="{{user.is_vip==1}}"><view class="cu-tag badge bg-yellow cuIcon-vip"></view></block></view><view class="{{[isPC?'text-xl margin-top pc-username':'text-xl margin-top']}}"><text>{{"你好，"+user.nickname}}</text></view><view class="{{[isPC?'margin-top-sm pc-sign':'margin-top-sm']}}"><text class="sign">相信美好的事情即将发生</text></view><image class="gif-wave" mode="scaleToFill" src="https://learnfile.20230611.cn/learnAppClient/cb/de/cbde6f5e83388a096d28fab7339d56fd.gif"></image></view><view class="{{[isPC?'padding flex text-center text-grey bg-white shadow-warp pc-stats-container':'padding flex text-center text-grey bg-white shadow-warp']}}"><view data-event-opts="{{[['tap',[['copyTap',['$0'],['user.id']]]]]}}" class="flex flex-sub flex-direction solid-right" bindtap="__e"><view class="{{[isPC?'text-xxl text-orange pc-stat-number':'text-xxl text-orange']}}">{{user.id}}</view><view class="{{[isPC?'margin-top-sm pc-stat-label':'margin-top-sm']}}"><text class="cuIcon-cuIcon"></text>编号</view></view><view class="flex flex-sub flex-direction solid-right" data-url="{{paths.rechargeMember}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="{{[isPC?'text-xxl text-blue pc-stat-number':'text-xxl text-blue']}}">{{user.vip_name}}</view><view class="{{[isPC?'margin-top-sm pc-stat-label':'margin-top-sm']}}"><text class="cuIcon-peoplefill"></text>级别</view></view><view class="flex flex-sub flex-direction" data-url="{{paths.score}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="{{[isPC?'text-xxl text-green pc-stat-number':'text-xxl text-green']}}">{{user.score}}</view><view class="{{[isPC?'margin-top-sm pc-stat-label':'margin-top-sm']}}"><text class="cuIcon-rechargefill"></text>积分</view></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>账户管理</view></view><view class="{{[gridClasses]}}"><block wx:if="{{!user.openid}}"><view class="{{[isPC?'cu-item pc-menu-item':'cu-item']}}" data-url="{{paths.wechat}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="{{[isPC?'menu-image pc-menu-image':'menu-image']}}" style="{{(isPC?'width: 100%;':'width: 100%;')}}" mode="aspectFit" src="/static/img/wechat.png"></image><text class="{{[isPC?'pc-menu-text':'']}}">绑定微信</text></view></block><block wx:if="{{!appIsAudit&&!user.email}}"><view class="{{[isPC?'cu-item pc-menu-item':'cu-item']}}" data-url="{{paths.email}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="{{[isPC?'menu-image pc-menu-image':'menu-image']}}" style="{{(isPC?'width: 100%;':'width: 100%;')}}" mode="aspectFit" src="/static/img/email.png"></image><text class="{{[isPC?'pc-menu-text':'']}}">绑定邮箱</text></view></block><block wx:if="{{!appIsAudit&&!user.mobile}}"><view class="{{[isPC?'cu-item pc-menu-item':'cu-item']}}" data-url="{{paths.mobile}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="{{[isPC?'menu-image pc-menu-image':'menu-image']}}" style="{{(isPC?'width: 100%;':'width: 100%;')}}" mode="aspectFit" src="/static/img/phone.png"></image><text class="{{[isPC?'pc-menu-text':'']}}">绑定手机</text></view></block><view class="{{[isPC?'cu-item pc-menu-item':'cu-item']}}" data-url="{{paths.password}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="{{[isPC?'menu-image pc-menu-image':'menu-image']}}" style="{{(isPC?'width: 100%;':'width: 100%;')}}" mode="aspectFit" src="/static/img/password.png"></image><text class="{{[isPC?'pc-menu-text':'']}}">设置密码</text></view><view class="{{[isPC?'cu-item pc-menu-item':'cu-item']}}" data-url="{{paths.info}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="{{[isPC?'menu-image pc-menu-image':'menu-image']}}" style="{{(isPC?'width: 100%;':'width: 100%;')}}" mode="aspectFit" src="/static/img/user-male-circle.png"></image><text class="{{[isPC?'pc-menu-text':'']}}">个人资料</text></view></view><view class="{{[isPC?'cu-bar bg-white solid-bottom margin-top-xs pc-section-bar':'cu-bar bg-white solid-bottom margin-top-xs']}}"><view class="action"><text class="text-orange"></text>学习管理</view></view><view class="{{[gridClasses]}}"><view class="{{[isPC?'cu-item pc-menu-item':'cu-item']}}" data-url="{{paths.myCourse}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="{{[isPC?'menu-image pc-menu-image':'menu-image']}}" style="{{(isPC?'width: 100%;':'width: 100%;')}}" mode="aspectFit" src="/static/img/book.png"></image><text>我的题库</text></view><view class="cu-item" data-url="{{paths.myErrorQuestion}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/error.png"></image><text>我的错题</text></view><view class="cu-item" data-url="{{paths.myCollect}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/bookmark.png"></image><text>我的收藏</text></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>我的服务</view></view><view class="cu-list grid col-5 no-border"><block wx:if="{{!appIsAudit}}"><view class="cu-item" data-url="{{paths.about}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/about.png"></image><text>关于我们</text></view></block><block wx:if="{{appPlatform==20||appPlatform==21}}"><view data-event-opts="{{[['tap',[['showOnlineServiceTap']]]]}}" class="cu-item" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/customer-support.png"></image><text>在线客服</text></view></block><view class="cu-item" data-url="{{paths.opinion}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/feeback.png"></image><text>意见反馈</text></view><block wx:if="{{user.is_agent==1}}"><view class="cu-item" data-url="{{paths.promote}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/share.png"></image><text>推广中心</text></view></block></view></view></block><confirm vue-id="380011e0-1" title="接入提醒" content="在线客服属于兼职，回复可能会不及时，请先留言，最晚会在12小时内回复您。" status="{{showOnlineServiceNotice}}" confirmText="知道啦，我要接入客服" confirmTextClass="cuIcon-servicefill" confirmButtonOpenType="contact" data-event-opts="{{[['^updateStatus',[['__set_sync',['$0','showOnlineServiceNotice','$event'],['']]]]]}}" bind:updateStatus="__e" bind:__l="__l"></confirm></view>